2025-08-18 14:32:18,031 - celery.redirected - WARNING - 🔍 Debug logging enabled. Log file: debug_logs/report_generation_debug_20250818_143218.log
2025-08-18 14:32:18,031 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-18 14:32:18,031 - DEBUG_SEPARATOR - INFO - 🎯 REPORT GENERATION START - Task ID: 1b3b6706-3fb1-4868-be46-bb5cf6e0de4b
2025-08-18 14:32:18,031 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-18 14:32:18,031 - REPORT_REQUEST - INFO - 📝 Original Question: 'Which institution has the most students?'
2025-08-18 14:32:18,031 - REPORT_REQUEST - INFO - 🆔 Task ID: 1b3b6706-3fb1-4868-be46-bb5cf6e0de4b
2025-08-18 14:32:18,031 - REPORT_REQUEST - INFO - ⏰ Started at: 2025-08-18T14:32:18.031652
2025-08-18 14:32:18,169 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.137s]
2025-08-18 14:32:18,169 - ELASTICSEARCH_STATE - INFO - 📊 Total documents in index 'deep_research': 192
2025-08-18 14:32:18,299 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_count [status:200 duration:0.129s]
2025-08-18 14:32:18,299 - ELASTICSEARCH_STATE - INFO - ✅ Documents with data_returned=True: 105
2025-08-18 14:32:18,433 - elastic_transport.transport - INFO - POST http://54.246.247.31:9200/deep_research/_search [status:200 duration:0.133s]
2025-08-18 14:32:18,433 - ELASTICSEARCH_STATE - INFO - 💬 Unique conversation IDs (10):
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'Which institution has the most students?' (42 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'How are boys performing at ITC University?' (25 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'How are girls performing at ITC University?' (22 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'What institution has the most students?' (15 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'Give me a report on student population across all institutions.' (12 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'How many students are there?' (12 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'From what institution do student owe the most fees?' (9 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'What is the fee owing situtation at ITC University?' (9 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees' (9 docs)
2025-08-18 14:32:18,434 - ELASTICSEARCH_STATE - INFO -   - 'Which institutions owe most fees?' (8 docs)
2025-08-18 14:32:18,435 - DEBUG_SEPARATOR - INFO - 
================================================================================
2025-08-18 14:32:18,435 - DEBUG_SEPARATOR - INFO - 🎯 STREAMING REPORT GENERATION STARTED
2025-08-18 14:32:18,435 - DEBUG_SEPARATOR - INFO - ================================================================================
2025-08-18 14:32:18,435 - STREAMING_REPORT_PIPELINE - INFO - 🎯 Original question: Which institution has the most students?
2025-08-18 14:32:18,435 - STREAMING_REPORT_PIPELINE - INFO - 📋 Include data gaps: False
2025-08-18 14:32:18,435 - app.chains.composite.report_pipeline - INFO - generating_uninformed_outline
