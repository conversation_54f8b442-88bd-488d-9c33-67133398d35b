"use client";

import CustomDropdown from '@/components/CustomDropdown';
import TextInput from '@/components/TextInput'
import TransparentDropdown from '@/components/TransparentDropdown';
import React, { useState, useRef, useEffect } from 'react'
import Message, { MessageProps } from './message';
import Carousel from '@/components/Carousel';
import { ThemeSwitcher } from '@/components/theme-switcher';
import { ReportGenerator } from '@/components/ReportGenerator';
import { GenerationSteps } from '@/components/GenerationSteps';
import { useStreamingReport, ReportGenerationRequest } from '@/services/reportApi';

const options = [
    { label: "Deep Research", value: "deep-research" },
    { label: "OSIS", value: "osis" }
];

const initMessages: MessageProps[] = [
    {
        from: "human",
        type: "text",
        content: "Hi there! Can you summarize this report for me?"
    },
    {
        from: "ai",
        type: "text",
        content: "Of course! Please share the text or file you'd like me to summarize."
    },
    {
        from: "human",
        type: "text",
        content: "Here’s the data: Sales increased by 20% in Q2 and customer retention improved by 10%."
    },
    {
        from: "ai",
        type: "text",
        content: "Summary: In Q2, sales rose 20%, and customer retention improved by 10%, indicating solid business performance."
    },
    {
        from: "human",
        type: "text",
        content: "Perfect. Can you also give me a visual representation?"
    },
    {
        from: "ai",
        type: "text",
        content: "Sure. I’ll generate a graph based on the data you provided."
    },
];


export default function Page() {
    const [messages, setMessages] = useState<MessageProps[]>([])
    const [selected, setSelected] = useState("deep-research");
    const [activeTab, setActiveTab] = useState<'chat' | 'reports'>('chat');
    const [isGeneratingReport, setIsGeneratingReport] = useState(false);

    // Use the streaming report hook
    const {
        isGenerating,
        progress,
        sections,
        currentSection,
        finalResult,
        error,
        startStreamingReport
    } = useStreamingReport();

    // Get current generation status from progress
    const currentStatus = progress.length > 0 ? progress[progress.length - 1].status : '';
    console.debug('[ChatPage] progress events count:', progress.length);
    if (progress.length > 0) {
        const last = progress[progress.length - 1];
        console.debug('[ChatPage] last progress event:', last.status, last);
    }

    const messagesEndRef = useRef<HTMLDivElement>(null);

    // Auto-scroll to bottom when new messages arrive
    useEffect(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages]);

    // Handle streaming report updates
    useEffect(() => {
        if (isGeneratingReport && (sections.length > 0 || finalResult)) {
            setMessages(prev => {
                // Find existing report message or create new one
                const reportMessageIndex = prev.findIndex(msg =>
                    msg.type === "report" && msg.from === "ai"
                );

                const reportData = finalResult || { sections, data: {}, outline: "", total_figures: 0 };
                // Report is still streaming if we're generating OR we don't have the final result yet
                const isReportStreaming = isGenerating || !finalResult;

                console.log('📊 Report streaming status:', {
                    isGenerating,
                    currentStatus,
                    isReportStreaming,
                    finalResult: !!finalResult,
                    hasCompleteFinalResult: !!finalResult
                });

                const reportMessage: MessageProps = {
                    from: "ai",
                    type: "report",
                    content: finalResult ? "Here's your generated report:" : "Generating your report...",
                    reportData: reportData,
                    // Add streaming props
                    isStreaming: isReportStreaming,
                    currentStatus: currentStatus,
                    streamingSections: sections,
                    currentSectionIndex: currentSection
                };

                if (reportMessageIndex >= 0) {
                    // Update existing report message
                    const newMessages = [...prev];
                    newMessages[reportMessageIndex] = reportMessage;
                    return newMessages;
                } else {
                    // Remove loading message and add report message
                    const filteredMessages = prev.filter(msg =>
                        !msg.content?.includes("Generating your report... This may take a few moments.")
                    );
                    return [...filteredMessages, reportMessage];
                }
            });
        }

        // Mark as complete when final result is available
        if (finalResult && isGeneratingReport) {
            setIsGeneratingReport(false);
        }
    }, [sections, finalResult, isGeneratingReport]);

    // Handle generation steps display
    useEffect(() => {
        if (isGenerating && currentStatus) {
            setMessages(prev => {
                // Remove any existing generation steps message
                const filteredMessages = prev.filter(msg =>
                    !msg.content?.includes("GENERATION_STEPS_PLACEHOLDER")
                );

                // Add generation steps message if not already present
                const hasGenerationSteps = prev.some(msg =>
                    msg.content?.includes("GENERATION_STEPS_PLACEHOLDER")
                );

                if (!hasGenerationSteps) {
                    const stepsMessage: MessageProps = {
                        from: "ai",
                        type: "text",
                        content: "GENERATION_STEPS_PLACEHOLDER"
                    };
                    return [...filteredMessages, stepsMessage];
                }

                return prev;
            });
        } else if (!isGenerating && currentStatus === 'complete') {
            // Remove generation steps when complete
            setMessages(prev => prev.filter(msg =>
                !msg.content?.includes("GENERATION_STEPS_PLACEHOLDER")
            ));
        }
    }, [isGenerating, currentStatus]);

    const handleMessageSubmit = async (message: string) => {
        // Add user message
        const userMessage: MessageProps = {
            from: "human",
            type: "text",
            content: message
        };
        setMessages(prev => [...prev, userMessage]);

        // Don't add a loading message - the generation steps will show instead

        try {
            setIsGeneratingReport(true);

            const request: ReportGenerationRequest = {
                original_question: message,
                include_data_gaps: false // Default to false for chat
            };

            await startStreamingReport(request, true); // Use streaming mode
        } catch (err) {
            console.error('Error generating report:', err);

            // Remove generation steps and add error message
            setMessages(prev => {
                const filtered = prev.filter(msg =>
                    !msg.content?.includes("Generating your report") &&
                    !msg.content?.includes("GENERATION_STEPS_PLACEHOLDER")
                );
                return [...filtered, {
                    from: "ai",
                    type: "text",
                    content: "Sorry, I encountered an error while generating your report. Please try again."
                }];
            });
            setIsGeneratingReport(false);
        }
    };



    return (
        <div className='text-foreground flex-1 flex flex-col overflow-y-auto'>
            {/* Tab Navigation */}
            <div className='flex justify-between items-center pt-4 px-4'>
                {/* <div className='flex gap-1 bg-muted rounded-lg p-1'>
                    <button
                        onClick={() => setActiveTab('chat')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                            activeTab === 'chat'
                                ? 'bg-background text-foreground shadow-sm'
                                : 'text-muted-foreground hover:text-foreground'
                        }`}
                    >
                        Chat
                    </button>
                    <button
                        onClick={() => setActiveTab('reports')}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                            activeTab === 'reports'
                                ? 'bg-background text-foreground shadow-sm'
                                : 'text-muted-foreground hover:text-foreground'
                        }`}
                    >
                        Reports
                    </button>
                </div> */}

                {/* <div className='flex items-center ml-auto gap-4'>
                    <ThemeSwitcher />
                    <div className='w-10 h-10 text-[1.25rem] rounded-full bg-secondary text-secondary-foreground flex justify-center items-center'>
                        K
                    </div>
                </div> */}
            </div>

            {/* Content Area */}
            <div className='flex-1 flex overflow-y-scroll w-full'>
                {activeTab === 'chat' ? (
                    <>
                        <div className='flex-1'>
                            <TransparentDropdown
                                value={selected}
                                setValue={setSelected}
                                options={options}
                                placeholder="Select an agent"
                            />
                        </div>
                        <div className='flex flex-col gap-5 h-full max-w-[50rem] mx-auto w-full'>
                            <div className='h-20'></div>
                            <div className='flex-1 relative'>
                                <div className='absolute flex flex-col-reverse w-full h-full'>
                                    {
                                        messages?.length > 0 ?

                                            <div className='flex flex-col gap-14 pb-20 overflow-y-auto [scrollbar-width:none] [&::-webkit-scrollbar]:hidden'>
                                                {
                                                    messages?.map((message, index) => (
                                                        <div key={index}>
                                                            {message.content === "GENERATION_STEPS_PLACEHOLDER" ? (
                                                                <div className="max-w-full mr-auto">
                                                                    <GenerationSteps
                                                                        currentStatus={currentStatus}
                                                                        isGenerating={isGenerating}
                                                                    />
                                                                </div>
                                                            ) : (
                                                                <Message
                                                                    {...message}
                                                                    // Use the streaming status from the message itself, don't override it
                                                                    currentStatus={currentStatus}
                                                                    streamingSections={sections}
                                                                    currentSectionIndex={currentSection}
                                                                />
                                                            )}
                                                        </div>
                                                    ))
                                                }
                                            </div>
                                            :
                                            <div className='flex flex-col w-full h-full items-center justify-center pb-20'>
                                                <div className={`text-[1.9rem] font-semibold bg-[linear-gradient(45deg,#8ab4f8_45%,#ffffff_50%,#8ab4f8_55%)] bg-[length:300%_300%] bg-clip-text text-transparent animate-shimmer
                                                 `}>
                                                    Hello, I&apos;m Romeo
                                                </div>
                                                <div className='text-[1.5rem] font-[600] text-muted-foreground'>
                                                    Powered By IT Consortium
                                                </div>
                                                <div className='text-sm text-muted-foreground mt-4 text-center max-w-md'>
                                                    Ask me to generate a report and I&apos;ll create visualizations and insights for you!
                                                </div>
                                            </div>
                                    }
                                    <div className='absolute w-full h-20 bottom-0 bg-gradient-to-b from-transparent to-background'>
                                    </div>
                                </div>
                            </div>

                            <TextInput
                                placeholder='Ask me to generate a report...'
                                onSubmit={handleMessageSubmit}
                                disabled={isGeneratingReport}
                            />
                            <div ref={messagesEndRef} />
                        </div>
                        <div className='flex-1'>
                            <div className='flex items-center justify-end ml-auto gap-4'>
                                <ThemeSwitcher />
                                <div className='w-10 h-10 text-[1.25rem] rounded-full bg-secondary text-secondary-foreground flex justify-center items-center'>
                                    K
                                </div>
                            </div>
                        </div>
                    </>
                ) : (
                    /* Reports Tab Content */
                    <div className='flex-1 max-w-6xl overflow-y-auto mx-auto p-6'>
                        <ReportGenerator />
                    </div>
                )}
            </div>
        </div>
    )
}


