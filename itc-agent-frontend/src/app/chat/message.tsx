"use client";

import React, { useEffect } from 'react'
import ReactMarkdown from 'react-markdown'
import { ReportChart, ChartData } from '@/components/ReportChart'
import { ReportTable } from '@/components/ReportTable'
import { ReportResult } from '@/services/reportApi'
import { ConversationalReport } from '@/components/ConversationalReport'
import { TextToSpeechButton } from '@/components/TextToSpeechButton'
import { useVoicePreferences } from '@/contexts/VoicePreferencesContext'
import { useTextToSpeech } from '@/hooks/useTextToSpeech'

export interface MessageProps {
    from: "ai" | "human";
    type: "text" | "table" | "graph" | "report" | "area_chart" | "bar_chart" | "pie_chart" | "line_chart";
    content?: string;
    chartData?: ChartData;
    tableData?: Record<string, unknown>[];
    reportData?: ReportResult;
    // Streaming properties
    isStreaming?: boolean;
    currentStatus?: string;
    streamingSections?: string[];
    currentSectionIndex?: number;
}

export default function Message({
    from,
    type,
    content,
    chartData,
    tableData,
    reportData,
    isStreaming,
    currentStatus,
    streamingSections,
    currentSectionIndex
}: MessageProps) {
    const isAI = from === "ai";
    const { preferences } = useVoicePreferences();
    const { speak } = useTextToSpeech();

    // Extract text content for TTS
    const getTextContent = (): string => {
        if (type === "text" && content) {
            // For text messages, use the content directly
            return content;
        }

        if (type === "report") {
            // For report messages, extract actual report content, not the hardcoded intro text
            if (reportData && reportData.sections) {
                // Extract text from all report sections
                const reportText = reportData.sections
                    .map(section => section || '')
                    .filter(section => section.trim().length > 0)
                    .join('\n\n');

                if (reportText.trim()) {
                    return reportText;
                }
            }

            // Fallback to content only if it's not the hardcoded intro text
            if (content &&
                !content.includes("Here's your generated report") &&
                !content.includes("Generating your report")) {
                return content;
            }
        }

        return '';
    };

    const textContent = getTextContent();

    // Auto-read AI responses if enabled (only for text messages, reports handle their own auto-read)
    useEffect(() => {
        if (isAI && textContent && preferences.autoReadAIResponses && !isStreaming && type === "text") {
            // Small delay to ensure the message is fully rendered
            const timer = setTimeout(() => {
                speak(textContent);
            }, 500);

            return () => clearTimeout(timer);
        }
    }, [isAI, textContent, preferences.autoReadAIResponses, isStreaming, type, speak]);

    return (
        <div className={`${isAI ? 'max-w-full mr-auto text-foreground' : 'max-w-[40rem] text-card-foreground ml-auto bg-secondary px-4 py-4 rounded-l-3xl rounded-br-3xl'}`}>
            {/* AI message header with TTS button */}
            {isAI && textContent && type === "text" && (
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                        <span className="text-xs text-muted-foreground">AI Assistant</span>
                        <TextToSpeechButton
                            text={textContent}
                            size="sm"
                            className="opacity-60 hover:opacity-100"
                        />
                    </div>
                </div>
            )}

            {/* Text content with markdown support */}
            {type === "text" && (
                <div className="prose max-w-none">
                    <ReactMarkdown>{content || ''}</ReactMarkdown>
                </div>
            )}

            {/* Chart types */}
            {(type === "graph" || type === "area_chart" || type === "bar_chart" || type === "pie_chart" || type === "line_chart") && chartData && (
                <div className="mt-2">
                    <ReportChart chartData={chartData} />
                </div>
            )}

            {/* Table type */}
            {type === "table" && tableData && (
                <div className="mt-2">
                    <ReportTable data={tableData} />
                </div>
            )}

            {/* Report type - conversational format like ChatGPT */}
            {type === "report" && reportData && (() => {
                const autoReadValue = isAI && preferences.autoReadAIResponses;
                console.log('📝 Message component passing autoRead:', {
                    isAI,
                    autoReadAIResponses: preferences.autoReadAIResponses,
                    autoReadValue,
                    isStreaming,
                    type
                });
                return (
                    <ConversationalReport
                        reportData={reportData}
                        content={content}
                        isStreaming={isStreaming}
                        currentStatus={currentStatus}
                        streamingSections={streamingSections}
                        currentSectionIndex={currentSectionIndex}
                        autoRead={autoReadValue}
                    />
                );
            })()}

            {/* Legacy report type - combination of text and single data visualizations */}
            {type === "report" && !reportData && (
                <div className="space-y-4">
                    {content && (
                        <div className="prose max-w-none">
                            <ReactMarkdown>{content}</ReactMarkdown>
                        </div>
                    )}
                    {chartData && (
                        <div className="my-6">
                            <ReportChart chartData={chartData} />
                        </div>
                    )}
                    {tableData && (
                        <div className="my-6">
                            <ReportTable data={tableData} />
                        </div>
                    )}
                </div>
            )}
        </div>
    )
}
